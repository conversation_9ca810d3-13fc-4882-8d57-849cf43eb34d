# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## CRITICAL: MANDATORY FIRST STEP
Before doing anything, read theses key folders that contain critical information:
- .cursor/rules/ - Development standards and patterns
- canbrax_configmatrix/docs_extend/ - Comprehensive technical documentation
- Individual module docs/ folders - Module-specific documentation
## Project Overview

This is an Odoo 18 development environment for ITMS Group focused on custom Odoo modules and Canbrax product configuration systems. The codebase contains:

- **canbrax-odoo/**: Custom Canbrax modules (configmatrix, stock management, Xero integration)
- **odoo_itms_apps/**: ITMS-developed Odoo modules (46+ modules)
- **odoo/**: Core Odoo 18 framework
- **design-themes/**: Website themes
- Various test scripts and log filtering utilities

## Core Architecture

### Main Components
- **canbrax_configmatrix**: Dynamic product configuration with BOM generation, visual SVG previews, and mesh panel calculations
- **canbrax_stock**: Stock management extensions  
- **sync_xero_connector**: Xero accounting integration
- **itms_***: Various ITMS utility modules (multi-company, chatter, reports, etc.)

### Key Module: canbrax_configmatrix
This is the primary business logic module featuring:
- Dynamic product configuration interface with conditional visibility
- Automatic BOM generation based on configurations  
- Complex calculated fields using JavaScript-to-Python formula conversion
- Visual SVG component rendering
- Mesh panel calculation and inventory management
- Integration with sales, manufacturing, and stock modules

## Development Commands

### Python Environment
```bash
# This appears to be a standard Python/Odoo setup
# No specific build commands found - likely uses standard Odoo commands
```

### Testing
```bash
# 1,475+ test files found in the codebase
# Test specific modules with pytest or standard Odoo testing
python -m pytest test_*.py  # For standalone test scripts
```

### Log Analysis
```bash
# Use provided log filtering utilities for debugging
python log_filter.py log/odoo-server.log output.log --tags BOM_PREVIEW_DEBUG QUANTITY_DEBUG
python simple_log_filter.py log/odoo-server.log filtered.log QUANTITY_DEBUG
```

## Critical Development Rules (from .cursor/rules/)

### Odoo 18 Compliance - CRITICAL
- **Use `<list>` instead of `<tree>`** for list views
- **Use direct attributes** instead of `attrs` (e.g., `invisible="condition"`)
- **Use `self.env._()` for translations** (not `_()`)
- **Follow Odoo 18 XML view structure** with modern syntax
- **Escape comparison operators** in XML templates (`&lt;`, `&gt;`, `&amp;`)
- **Use proper label tags** with `for` attribute or `class="o_form_label"`
- **Quote widget options** with single quotes: `options="{'mode': 'json'}"`
- **Use proper button attributes**: `type="object"`, `string="Button Text"`

### @api.onchange Best Practices - CRITICAL
- **NEVER put business logic in @api.onchange methods**
- @api.onchange only triggers in UI during user interaction
- @api.onchange does NOT run during create()/write(), server automation, data imports, or API calls
- **ALWAYS use standalone methods for business logic** 
- Call standalone methods from both @api.onchange (UI) and create()/write() (backend)
- **Separate UI logic (@api.onchange) from backend logic (create/write)**

### Code Standards
- Follow PEP 8 for Python files
- Use proper Odoo 18 patterns and avoid deprecated methods
- Follow Odoo standard conventions for all development
- Ask clarifying questions for unclear requirements before implementation
- Document new modules in their 'docs' folders comprehensively

## File Structure Patterns

### Odoo Module Structure
```
module_name/
├── __manifest__.py          # Module definition
├── models/                  # Business logic
├── views/                   # UI definitions  
├── security/               # Access rights
├── static/src/             # Frontend assets
├── wizards/                # Popup interfaces
└── data/                   # Initial data
```

### Common File Locations
- Business logic: `models/*.py`
- UI definitions: `views/*.xml` 
- Frontend code: `static/src/js/*.js`, `static/src/css/*.css`
- Security: `security/ir.model.access.csv`, `security/security.xml`

## Key Integration Points

### Mesh Calculation System
Located in `canbrax_configmatrix`, handles complex mesh panel calculations using JavaScript formulas converted to Python. Critical for BOM generation.

### Multi-Company Support  
Many modules extend Odoo's multi-company functionality through `itms_base_multi_company` and related modules.

### External Integrations
- Xero accounting via `sync_xero_connector`
- Firebase push notifications via `firebase_push_notification`
- Various payment gateways and APIs

## Debugging

### Log Tags
The codebase uses extensive debug logging with tags:
- `[BOM_PREVIEW_DEBUG]` - BOM generation debugging
- `[QUANTITY_DEBUG]` - Quantity calculations  
- `[MESH_DEBUG]` - Mesh panel calculations
- Use log filtering scripts to extract specific debug information

### Common Issues
- Mesh calculation failures in configmatrix (see MESH_CALCULATION_BUG_REPORT.md)
- Multi-company data isolation
- Complex formula evaluation in configuration templates

## Module Documentation Requirements

### Reading Module Documentation - CRITICAL RULE
When working on any module, you MUST:
1. **Read ALL documentation in the module's docs folder** before making changes
2. **Understand the module's purpose, architecture, and technical structure**
3. **Reference the comprehensive documentation in canbrax_configmatrix/docs_extend/**
4. **Follow the patterns and guidelines documented in each module**

### Key Documentation Locations
- `canbrax_configmatrix/docs_extend/` - Comprehensive technical documentation
- `canbrax_configmatrix/docs/` - Core module documentation  
- Individual module `docs/` folders - Module-specific documentation
- `.cursor/rules/` - Development standards and patterns

## OWL 2.0 Frontend Development

### Component Structure (Odoo 18)
```javascript
/** @odoo-module **/
import { Component, useState, onMounted } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

export class ExampleComponent extends Component {
    static template = "module_name.TemplateName";
    
    setup() {
        this.state = useState({
            loading: false,
            data: {},
        });
        
        this.rpc = useService("rpc");
        this.notification = useService("notification");
    }
}
```

### Widget Registration (Odoo 18)
```javascript
// ✅ CORRECT - Use component property
registry.category("fields").add("custom_widget", {
    component: CustomWidgetComponent,
});

// ❌ INCORRECT - Direct widget registration
registry.category("fields").add("custom_widget", CustomWidget);
```