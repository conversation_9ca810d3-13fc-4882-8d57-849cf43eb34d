<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Calculate effective dimensions -->
        <record id="calc_field_sws_left_height" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_sws_left_height</field>
            <field name="description">Left height = SQRH + TLT + BLB (minimum 150mm)</field>
            <field name="formula">Math.max(150, (sws_wscrn_square_make_height_mm || 0) + (sws_wscrn_top_left_top_out_of_square_dimension_mm || 0) + (sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm || 0))</field>
            <field name="sequence">100</field>
            <field name="category">basic</field>
            <field name="data_type">number</field>
            <field name="depends_on">sws_wscrn_square_make_height_mm,sws_wscrn_top_left_top_out_of_square_dimension_mm,sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm</field>
        </record>

        <record id="calc_field_sws_right_height" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_sws_right_height</field>
            <field name="description">Right height = SQRH + TRT + BRB (minimum 150mm)</field>
            <field name="formula">Math.max(150, (sws_wscrn_square_make_height_mm || 0) + (sws_wscrn_top_right_top_out_of_square_dimension_mm || 0) + (sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm || 0))</field>
            <field name="sequence">101</field>
            <field name="category">basic</field>
            <field name="data_type">number</field>
            <field name="depends_on">sws_wscrn_square_make_height_mm,sws_wscrn_top_right_top_out_of_square_dimension_mm,sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm</field>
        </record>

        <!-- Calculate diagonals -->
        <record id="calc_field_sws_diagonal_tlbr" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_sws_diagonal_tlbr</field>
            <field name="description">Top-left to bottom-right diagonal</field>
            <field name="formula">Math.sqrt(Math.pow((sws_wscrn_square_make_height_mm || 0) + (sws_wscrn_top_left_top_out_of_square_dimension_mm || 0) + (sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm || 0), 2) + Math.pow((sws_wscrn_square_make_width_mm || 0) + (sws_wscrn_top_left_side_out_of_square_dimension_mm || 0) + (sws_wscrn_bottom_right_side_out_of_square_dimension_mm || 0), 2))</field>
            <field name="sequence">110</field>
            <field name="category">basic</field>
            <field name="data_type">number</field>
            <field name="depends_on">sws_wscrn_square_make_height_mm,sws_wscrn_square_make_width_mm,sws_wscrn_top_left_top_out_of_square_dimension_mm,sws_wscrn_top_left_side_out_of_square_dimension_mm,sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm,sws_wscrn_bottom_right_side_out_of_square_dimension_mm</field>
        </record>

        <!-- Calculate corner angles using law of cosines -->
        <record id="calc_field_sws_corner_angle_a" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_sws_corner_angle_a</field>
            <field name="description">Corner angle A in degrees</field>
            <field name="formula">
                (() => {
                    const SQRH = sws_wscrn_square_make_height_mm || 0;
                    const SQRW = sws_wscrn_square_make_width_mm || 0;
                    const TLT = sws_wscrn_top_left_top_out_of_square_dimension_mm || 0;
                    const TRT = sws_wscrn_top_right_top_out_of_square_dimension_mm || 0;
                    const TLS = sws_wscrn_top_left_side_out_of_square_dimension_mm || 0;
                    const TRS = sws_wscrn_top_right_side_out_of_square_dimension_mm || 0;
                    const BLB = sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm || 0;
                    const BRB = sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm || 0;
                    const BLS = sws_wscrn_bottom_left_side_out_of_square_dimension_mm || 0;
                    const BRS = sws_wscrn_bottom_right_side_out_of_square_dimension_mm || 0;
                    
                    // Calculate side lengths
                    const AB = Math.sqrt(Math.pow(Math.abs(TLT - TRT), 2) + Math.pow(SQRW + TLS + TRS, 2));
                    const AD = Math.sqrt(Math.pow(Math.abs(TLS - BLS), 2) + Math.pow(SQRH + TLT + BLB, 2));
                    const BD = Math.sqrt(Math.pow((SQRH + TRT + BRB), 2) + Math.pow((SQRW + TRS + BLS), 2));
                    
                    // Law of cosines for angle A
                    const cosA = (Math.pow(AD, 2) + Math.pow(AB, 2) - Math.pow(BD, 2)) / (2 * AD * AB);
                    return Math.acos(Math.max(-1, Math.min(1, cosA))) * (180 / Math.PI);
                })()
            </field>
            <field name="sequence">120</field>
            <field name="category">condition</field>
            <field name="data_type">number</field>
            <field name="depends_on">sws_wscrn_square_make_height_mm,sws_wscrn_square_make_width_mm,sws_wscrn_top_left_top_out_of_square_dimension_mm,sws_wscrn_top_right_top_out_of_square_dimension_mm,sws_wscrn_top_left_side_out_of_square_dimension_mm,sws_wscrn_top_right_side_out_of_square_dimension_mm,sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm,sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm,sws_wscrn_bottom_left_side_out_of_square_dimension_mm,sws_wscrn_bottom_right_side_out_of_square_dimension_mm</field>
        </record>

        <!-- Calculate corner angle B -->
        <record id="calc_field_sws_corner_angle_b" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_sws_corner_angle_b</field>
            <field name="description">Corner angle B in degrees</field>
            <field name="formula">
                (() => {
                    const SQRH = sws_wscrn_square_make_height_mm || 0;
                    const SQRW = sws_wscrn_square_make_width_mm || 0;
                    const TLT = sws_wscrn_top_left_top_out_of_square_dimension_mm || 0;
                    const TRT = sws_wscrn_top_right_top_out_of_square_dimension_mm || 0;
                    const TLS = sws_wscrn_top_left_side_out_of_square_dimension_mm || 0;
                    const TRS = sws_wscrn_top_right_side_out_of_square_dimension_mm || 0;
                    const BLB = sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm || 0;
                    const BRB = sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm || 0;
                    const BLS = sws_wscrn_bottom_left_side_out_of_square_dimension_mm || 0;
                    const BRS = sws_wscrn_bottom_right_side_out_of_square_dimension_mm || 0;

                    // Calculate side lengths
                    const AB = Math.sqrt(Math.pow(Math.abs(TLT - TRT), 2) + Math.pow(SQRW + TLS + TRS, 2));
                    const BC = Math.sqrt(Math.pow(Math.abs(TRS - BRS), 2) + Math.pow(SQRH + TRT + BRB, 2));
                    const AC = Math.sqrt(Math.pow((SQRH + TLT + BRB), 2) + Math.pow((SQRW + TLS + BRS), 2));

                    // Law of cosines for angle B
                    const cosB = (Math.pow(AB, 2) + Math.pow(BC, 2) - Math.pow(AC, 2)) / (2 * AB * BC);
                    return Math.acos(Math.max(-1, Math.min(1, cosB))) * (180 / Math.PI);
                })()
            </field>
            <field name="sequence">121</field>
            <field name="category">condition</field>
            <field name="data_type">number</field>
            <field name="depends_on">sws_wscrn_square_make_height_mm,sws_wscrn_square_make_width_mm,sws_wscrn_top_left_top_out_of_square_dimension_mm,sws_wscrn_top_right_top_out_of_square_dimension_mm,sws_wscrn_top_left_side_out_of_square_dimension_mm,sws_wscrn_top_right_side_out_of_square_dimension_mm,sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm,sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm,sws_wscrn_bottom_left_side_out_of_square_dimension_mm,sws_wscrn_bottom_right_side_out_of_square_dimension_mm</field>
        </record>

        <!-- Calculate corner angle C -->
        <record id="calc_field_sws_corner_angle_c" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_sws_corner_angle_c</field>
            <field name="description">Corner angle C in degrees</field>
            <field name="formula">
                (() => {
                    const SQRH = sws_wscrn_square_make_height_mm || 0;
                    const SQRW = sws_wscrn_square_make_width_mm || 0;
                    const TLT = sws_wscrn_top_left_top_out_of_square_dimension_mm || 0;
                    const TRT = sws_wscrn_top_right_top_out_of_square_dimension_mm || 0;
                    const TLS = sws_wscrn_top_left_side_out_of_square_dimension_mm || 0;
                    const TRS = sws_wscrn_top_right_side_out_of_square_dimension_mm || 0;
                    const BLB = sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm || 0;
                    const BRB = sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm || 0;
                    const BLS = sws_wscrn_bottom_left_side_out_of_square_dimension_mm || 0;
                    const BRS = sws_wscrn_bottom_right_side_out_of_square_dimension_mm || 0;

                    // Calculate side lengths
                    const BC = Math.sqrt(Math.pow(Math.abs(TRS - BRS), 2) + Math.pow(SQRH + TRT + BRB, 2));
                    const CD = Math.sqrt(Math.pow(Math.abs(BLB - BRB), 2) + Math.pow(SQRW + BLS + BRS, 2));
                    const BD = Math.sqrt(Math.pow((SQRH + TRT + BRB), 2) + Math.pow((SQRW + TRS + BLS), 2));

                    // Law of cosines for angle C
                    const cosC = (Math.pow(BC, 2) + Math.pow(CD, 2) - Math.pow(BD, 2)) / (2 * BC * CD);
                    return Math.acos(Math.max(-1, Math.min(1, cosC))) * (180 / Math.PI);
                })()
            </field>
            <field name="sequence">122</field>
            <field name="category">condition</field>
            <field name="data_type">number</field>
            <field name="depends_on">sws_wscrn_square_make_height_mm,sws_wscrn_square_make_width_mm,sws_wscrn_top_left_top_out_of_square_dimension_mm,sws_wscrn_top_right_top_out_of_square_dimension_mm,sws_wscrn_top_left_side_out_of_square_dimension_mm,sws_wscrn_top_right_side_out_of_square_dimension_mm,sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm,sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm,sws_wscrn_bottom_left_side_out_of_square_dimension_mm,sws_wscrn_bottom_right_side_out_of_square_dimension_mm</field>
        </record>

        <!-- Calculate corner angle D -->
        <record id="calc_field_sws_corner_angle_d" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_sws_corner_angle_d</field>
            <field name="description">Corner angle D in degrees</field>
            <field name="formula">
                (() => {
                    const SQRH = sws_wscrn_square_make_height_mm || 0;
                    const SQRW = sws_wscrn_square_make_width_mm || 0;
                    const TLT = sws_wscrn_top_left_top_out_of_square_dimension_mm || 0;
                    const TRT = sws_wscrn_top_right_top_out_of_square_dimension_mm || 0;
                    const TLS = sws_wscrn_top_left_side_out_of_square_dimension_mm || 0;
                    const TRS = sws_wscrn_top_right_side_out_of_square_dimension_mm || 0;
                    const BLB = sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm || 0;
                    const BRB = sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm || 0;
                    const BLS = sws_wscrn_bottom_left_side_out_of_square_dimension_mm || 0;
                    const BRS = sws_wscrn_bottom_right_side_out_of_square_dimension_mm || 0;

                    // Calculate side lengths
                    const CD = Math.sqrt(Math.pow(Math.abs(BLB - BRB), 2) + Math.pow(SQRW + BLS + BRS, 2));
                    const AD = Math.sqrt(Math.pow(Math.abs(TLS - BLS), 2) + Math.pow(SQRH + TLT + BLB, 2));
                    const AC = Math.sqrt(Math.pow((SQRH + TLT + BRB), 2) + Math.pow((SQRW + TLS + BRS), 2));

                    // Law of cosines for angle D
                    const cosD = (Math.pow(CD, 2) + Math.pow(AD, 2) - Math.pow(AC, 2)) / (2 * CD * AD);
                    return Math.acos(Math.max(-1, Math.min(1, cosD))) * (180 / Math.PI);
                })()
            </field>
            <field name="sequence">123</field>
            <field name="category">condition</field>
            <field name="data_type">number</field>
            <field name="depends_on">sws_wscrn_square_make_height_mm,sws_wscrn_square_make_width_mm,sws_wscrn_top_left_top_out_of_square_dimension_mm,sws_wscrn_top_right_top_out_of_square_dimension_mm,sws_wscrn_top_left_side_out_of_square_dimension_mm,sws_wscrn_top_right_side_out_of_square_dimension_mm,sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm,sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm,sws_wscrn_bottom_left_side_out_of_square_dimension_mm,sws_wscrn_bottom_right_side_out_of_square_dimension_mm</field>
        </record>

        <!-- Main tolerance check field -->
        <record id="calc_field_sws_within_tolerance" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_sws_within_tolerance</field>
            <field name="description">Check if all corner angles are within 88-92 degree tolerance</field>
            <field name="formula">
                (() => {
                    if (sws_wscrn_hand_built_type !== 'yes') return true;
                    
                    const angles = [
                        _CALCULATED_sws_corner_angle_a,
                        _CALCULATED_sws_corner_angle_b,
                        _CALCULATED_sws_corner_angle_c,
                        _CALCULATED_sws_corner_angle_d
                    ];
                    
                    return angles.every(angle => angle &gt;= 88 &amp;&amp; angle &lt;= 92);
                })()
            </field>
            <field name="sequence">130</field>
            <field name="category">condition</field>
            <field name="data_type">boolean</field>
            <field name="depends_on">sws_wscrn_hand_built_type,_CALCULATED_sws_corner_angle_a,_CALCULATED_sws_corner_angle_b,_CALCULATED_sws_corner_angle_c,_CALCULATED_sws_corner_angle_d</field>
        </record>

        <!-- Requires special handling field -->
        <record id="calc_field_sws_requires_special_handling" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_sws_requires_special_handling</field>
            <field name="description">Requires special handling if hand built and outside tolerance</field>
            <field name="formula">sws_wscrn_hand_built_type === 'yes' &amp;&amp; !_CALCULATED_sws_within_tolerance</field>
            <field name="sequence">140</field>
            <field name="category">final</field>
            <field name="data_type">boolean</field>
            <field name="depends_on">sws_wscrn_hand_built_type,_CALCULATED_sws_within_tolerance</field>
        </record>

        <!-- Calculate pricing dimensions -->
        <record id="calc_field_sws_overall_height" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_sws_overall_height</field>
            <field name="description">Overall height calculation = max(TLT, TRT) + SQRH + max(BLB, BRB)</field>
            <field name="formula">
                (() => {
                    const SQRH = sws_wscrn_square_make_height_mm || 0;
                    const TLT = sws_wscrn_top_left_top_out_of_square_dimension_mm || 0;
                    const TRT = sws_wscrn_top_right_top_out_of_square_dimension_mm || 0;
                    const BLB = sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm || 0;
                    const BRB = sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm || 0;

                    return Math.max(TLT, TRT) + SQRH + Math.max(BLB, BRB);
                })()
            </field>
            <field name="sequence">150</field>
            <field name="category">basic</field>
            <field name="data_type">number</field>
            <field name="depends_on">sws_wscrn_square_make_height_mm,sws_wscrn_top_left_top_out_of_square_dimension_mm,sws_wscrn_top_right_top_out_of_square_dimension_mm,sws_wscrn_bottom_left_bottom_out_of_square_dimension_mm,sws_wscrn_bottom_right_bottom_out_of_square_dimension_mm</field>
        </record>

        <record id="calc_field_sws_overall_width" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_sws_overall_width</field>
            <field name="description">Overall width calculation = max(TLS, MLS, BLS) + SQRW + max(TRS, MRS, BRS)</field>
            <field name="formula">
                (() => {
                    const SQRW = sws_wscrn_square_make_width_mm || 0;
                    const TLS = sws_wscrn_top_left_side_out_of_square_dimension_mm || 0;
                    const MLS = sws_wscrn_middle_left_side_out_of_square_dimension_mm || 0;
                    const BLS = sws_wscrn_bottom_left_side_out_of_square_dimension_mm || 0;
                    const TRS = sws_wscrn_top_right_side_out_of_square_dimension_mm || 0;
                    const MRS = sws_wscrn_middle_right_side_out_of_square_dimension_mm || 0;
                    const BRS = sws_wscrn_bottom_right_side_out_of_square_dimension_mm || 0;

                    return Math.max(TLS, MLS, BLS) + SQRW + Math.max(TRS, MRS, BRS);
                })()
            </field>
            <field name="sequence">151</field>
            <field name="category">basic</field>
            <field name="data_type">number</field>
            <field name="depends_on">sws_wscrn_square_make_width_mm,sws_wscrn_top_left_side_out_of_square_dimension_mm,sws_wscrn_middle_left_side_out_of_square_dimension_mm,sws_wscrn_bottom_left_side_out_of_square_dimension_mm,sws_wscrn_top_right_side_out_of_square_dimension_mm,sws_wscrn_middle_right_side_out_of_square_dimension_mm,sws_wscrn_bottom_right_side_out_of_square_dimension_mm</field>
        </record>

        <!-- Calculate manufacturing dimensions -->
        <record id="calc_field_sws_final_height" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_sws_final_height</field>
            <field name="description">Final height = sws_wscrn_make_window_screen_height_mm or calculated if not provided</field>
            <field name="formula">sws_wscrn_make_window_screen_height_mm || _CALCULATED_sws_overall_height</field>
            <field name="sequence">160</field>
            <field name="category">final</field>
            <field name="data_type">number</field>
            <field name="depends_on">sws_wscrn_make_window_screen_height_mm,_CALCULATED_sws_overall_height</field>
        </record>

        <record id="calc_field_sws_final_width" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_sws_final_width</field>
            <field name="description">Final width = sws_wscrn_make_window_screen_width_mm or calculated if not provided</field>
            <field name="formula">sws_wscrn_make_window_screen_width_mm || _CALCULATED_sws_overall_width</field>
            <field name="sequence">161</field>
            <field name="category">final</field>
            <field name="data_type">number</field>
            <field name="depends_on">sws_wscrn_make_window_screen_width_mm,_CALCULATED_sws_overall_width</field>
        </record>

    </data>
</odoo>