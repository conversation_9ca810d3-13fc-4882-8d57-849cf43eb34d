# ConfigMatrix Documentation Index

This index provides an organized overview of all ConfigMatrix documentation files, helping you find the information you need quickly and efficiently.

## 📚 Core Documentation

### Getting Started
- **[README.md](../README.md)**: Main project overview, installation, and quick start guide
- **[00_OVERVIEW.md](00_OVERVIEW.md)**: High-level overview, core concepts, and system architecture
- **[07_USER_GUIDE.md](07_USER_GUIDE.md)**: Complete step-by-step user instructions for all user types

### Technical Documentation
- **[01_DATA_MODELS.md](01_DATA_MODELS.md)**: Complete database structure, model relationships, and field definitions
- **[06_DEVELOPER_GUIDE.md](06_DEVELOPER_GUIDE.md)**: Technical implementation details, development patterns, and extension guidance
- **[ODOO_18_GUIDELINES.md](ODOO_18_GUIDELINES.md)**: Odoo 18 specific syntax, best practices, and migration notes

## 🔧 Feature-Specific Documentation

### Core Features
- **[03_BOM_GENERATION.md](03_BOM_GENERATION.md)**: Bill of Materials creation process and component mapping
- **[04_MANUFACTURING_ORDER_CREATION.md](04_MANUFACTURING_ORDER_CREATION.md)**: Complete Manufacturing Order creation workflow for configurable products
- **[05_MANUFACTURING_TECHNICAL_IMPLEMENTATION.md](05_MANUFACTURING_TECHNICAL_IMPLEMENTATION.md)**: Technical implementation details for Manufacturing Order creation
- **[09_MANUFACTURING_USER_GUIDE.md](09_MANUFACTURING_USER_GUIDE.md)**: User guide for manufacturing configurable products
- **[12_DYNAMIC_FIELD_MATCHING.md](12_DYNAMIC_FIELD_MATCHING.md)**: Dynamic field behavior and conditional logic
- **[calcuated_fields.md](calcuated_fields.md)**: Formula-based computed fields and dependency tracking
- **[CALCULATED_FIELDS_COMPREHENSIVE.md](CALCULATED_FIELDS_COMPREHENSIVE.md)**: Comprehensive guide to calculated fields system with recent fixes
- **[DEPENDENCY_RESOLUTION_FIXES.md](DEPENDENCY_RESOLUTION_FIXES.md)**: Detailed documentation of dependency resolution and type conversion fixes
- **[svg_component_guide.md](svg_component_guide.md)**: Visual component rendering and SVG handling
- **[OPERATION_COSTS_HANDLER_ENHANCEMENT.md](OPERATION_COSTS_HANDLER_ENHANCEMENT.md)**: Operation costs handler enhancement with calculated fields integration

### Advanced Features
- **[ENHANCED_JSON_IMPORT.md](ENHANCED_JSON_IMPORT.md)**: Data import/export functionality and integration
- **[MANAGE_PRICING_COMPLETE.md](MANAGE_PRICING_COMPLETE.md)**: Pricing matrix and calculation systems
- **[08_MATRIX_USE.MD](08_MATRIX_USE.MD)**: Specific use cases and implementation scenarios

### Integration & Workflows
- **[10_WEBSITE_PORTAL.md](10_WEBSITE_PORTAL.md)**: Frontend integration and portal functionality
- **[bom_generation.md](bom_generation.md)**: Detailed BOM creation process and manufacturing integration
- **[MANUFACTURING_QUICK_REFERENCE.md](MANUFACTURING_QUICK_REFERENCE.md)**: Quick reference guide for Manufacturing Order creation workflow

## 🛠️ Technical Reference

### Code Patterns & Examples
- **[python_dictionary.md](python_dictionary.md)**: Python code patterns, data structures, and implementation examples
- **[dynamic_error_messages_examples.md](dynamic_error_messages_examples.md)**: Error handling patterns and user feedback examples
- **[lock_visibility_project_notes.md](lock_visibility_project_notes.md)**: UI state management and visibility controls
- **[OPERATION_COSTS_HANDLER_ENHANCEMENT.md](OPERATION_COSTS_HANDLER_ENHANCEMENT.md)**: JavaScript enhancement for operation costs with calculated fields

### Development & Maintenance
- **[codebase_analysis_cleanup_plan.md](codebase_analysis_cleanup_plan.md)**: Code quality analysis and optimization recommendations
- **[calculated_field_test_results.md](calculated_field_test_results.md)**: Testing results and validation procedures
- **[LOGGING_IMPLEMENTATION_GUIDE.md](LOGGING_IMPLEMENTATION_GUIDE.md)**: Comprehensive guide for adding logging back into the codebase when needed
- **[OPERATION_COST_TROUBLESHOOTING.md](OPERATION_COST_TROUBLESHOOTING.md)**: Comprehensive troubleshooting guide for operation cost calculation issues
- **[LOGGING_QUICK_REFERENCE.md](LOGGING_QUICK_REFERENCE.md)**: Quick reference for implementing logging in CanBrax ConfigMatrix

## 📊 Performance & Optimization

### Performance Analysis
- **[PERFORMANCE_ANALYSIS_AUTO_QUESTIONS.md](PERFORMANCE_ANALYSIS_AUTO_QUESTIONS.md)**: Automated performance analysis and optimization questions
- **[PERFORMANCE_FIX_IMPLEMENTATION_PLAN.md](PERFORMANCE_FIX_IMPLEMENTATION_PLAN.md)**: Performance improvement implementation strategies
- **[PERFORMANCE_OPTIMIZATION_IMPLEMENTATION_SUMMARY.md](PERFORMANCE_OPTIMIZATION_IMPLEMENTATION_SUMMARY.md)**: Performance optimization implementation summary
- **[PERFORMANCE_TESTING_GUIDE.md](PERFORMANCE_TESTING_GUIDE.md)**: Performance testing procedures and benchmarks

## 🔄 Development & Project Management

### Project Documentation
- **[22june_tasks.md](22june_tasks.md)**: Project tasks and development milestones
- **[22june_solutions.js](22june_solutions.js)**: JavaScript solutions and implementation examples

## 📋 Documentation by User Type

### For Administrators
1. **[README.md](../README.md)** - Start here for installation and setup
2. **[00_OVERVIEW.md](00_OVERVIEW.md)** - Understand the system architecture
3. **[07_USER_GUIDE.md](07_USER_GUIDE.md)** - Administrative setup and management
4. **[01_DATA_MODELS.md](01_DATA_MODELS.md)** - Database structure and relationships
5. **[MANAGE_PRICING_COMPLETE.md](MANAGE_PRICING_COMPLETE.md)** - Pricing matrix management

### For Sales Users
1. **[07_USER_GUIDE.md](07_USER_GUIDE.md)** - Sales configuration workflow
2. **[03_BOM_GENERATION.md](03_BOM_GENERATION.md)** - Understanding BOM generation
3. **[10_WEBSITE_PORTAL.md](10_WEBSITE_PORTAL.md)** - Customer portal features
4. **[08_MATRIX_USE.MD](08_MATRIX_USE.MD)** - Use case scenarios

### For Developers
1. **[06_DEVELOPER_GUIDE.md](06_DEVELOPER_GUIDE.md)** - Technical implementation details
2. **[01_DATA_MODELS.md](01_DATA_MODELS.md)** - Complete data model reference
3. **[ODOO_18_GUIDELINES.md](ODOO_18_GUIDELINES.md)** - Odoo 18 development standards
4. **[python_dictionary.md](python_dictionary.md)** - Code patterns and examples
5. **[codebase_analysis_cleanup_plan.md](codebase_analysis_cleanup_plan.md)** - Code quality guidelines

### For Builders/Contractors
1. **[07_USER_GUIDE.md](07_USER_GUIDE.md)** - Builder portal workflow
2. **[10_WEBSITE_PORTAL.md](10_WEBSITE_PORTAL.md)** - Portal functionality
3. **[svg_component_guide.md](svg_component_guide.md)** - Visual component usage
4. **[bom_generation.md](bom_generation.md)** - Manufacturing integration

### For Customers (Online)
1. **[07_USER_GUIDE.md](07_USER_GUIDE.md)** - Customer portal usage
2. **[10_WEBSITE_PORTAL.md](10_WEBSITE_PORTAL.md)** - Portal features and functionality

## 🔍 Documentation by Topic

### Configuration & Templates
- **[01_DATA_MODELS.md](01_DATA_MODELS.md)** - Template structure and field definitions
- **[12_DYNAMIC_FIELD_MATCHING.md](12_DYNAMIC_FIELD_MATCHING.md)** - Dynamic field behavior
- **[calcuated_fields.md](calcuated_fields.md)** - Calculated fields and formulas
- **[07_USER_GUIDE.md](07_USER_GUIDE.md)** - Template creation and management

### Pricing & Calculations
- **[MANAGE_PRICING_COMPLETE.md](MANAGE_PRICING_COMPLETE.md)** - Pricing matrix management
- **[03_BOM_GENERATION.md](03_BOM_GENERATION.md)** - Component pricing and BOM calculations
- **[calcuated_fields.md](calcuated_fields.md)** - Formula-based calculations

### Visual Components
- **[svg_component_guide.md](svg_component_guide.md)** - SVG rendering and visual components
- **[lock_visibility_project_notes.md](lock_visibility_project_notes.md)** - UI state management
- **[07_USER_GUIDE.md](07_USER_GUIDE.md)** - Visual component configuration

### Integration & Workflows
- **[10_WEBSITE_PORTAL.md](10_WEBSITE_PORTAL.md)** - Portal integration
- **[bom_generation.md](bom_generation.md)** - Manufacturing integration
- **[ENHANCED_JSON_IMPORT.md](ENHANCED_JSON_IMPORT.md)** - Data import/export
- **[07_USER_GUIDE.md](07_USER_GUIDE.md)** - Sales and manufacturing workflows

### Performance & Optimization
- **[PERFORMANCE_ANALYSIS_AUTO_QUESTIONS.md](PERFORMANCE_ANALYSIS_AUTO_QUESTIONS.md)** - Performance analysis
- **[PERFORMANCE_FIX_IMPLEMENTATION_PLAN.md](PERFORMANCE_FIX_IMPLEMENTATION_PLAN.md)** - Performance fixes
- **[PERFORMANCE_OPTIMIZATION_IMPLEMENTATION_SUMMARY.md](PERFORMANCE_OPTIMIZATION_IMPLEMENTATION_SUMMARY.md)** - Optimization summary
- **[PERFORMANCE_TESTING_GUIDE.md](PERFORMANCE_TESTING_GUIDE.md)** - Testing procedures

### Development & Technical
- **[06_DEVELOPER_GUIDE.md](06_DEVELOPER_GUIDE.md)** - Development patterns
- **[ODOO_18_GUIDELINES.md](ODOO_18_GUIDELINES.md)** - Odoo 18 standards
- **[python_dictionary.md](python_dictionary.md)** - Code patterns
- **[codebase_analysis_cleanup_plan.md](codebase_analysis_cleanup_plan.md)** - Code quality
- **[dynamic_error_messages_examples.md](dynamic_error_messages_examples.md)** - Error handling

## 🚀 Quick Reference

### Installation & Setup
1. **[README.md](../README.md)** - Installation instructions
2. **[07_USER_GUIDE.md](07_USER_GUIDE.md)** - Initial configuration
3. **[00_OVERVIEW.md](00_OVERVIEW.md)** - System overview

### Common Tasks
- **Create Template**: [07_USER_GUIDE.md](07_USER_GUIDE.md) → Template Management
- **Configure Pricing**: [MANAGE_PRICING_COMPLETE.md](MANAGE_PRICING_COMPLETE.md)
- **Set Up Portal**: [10_WEBSITE_PORTAL.md](10_WEBSITE_PORTAL.md)
- **Add Visual Components**: [svg_component_guide.md](svg_component_guide.md)
- **Create Calculated Fields**: [calcuated_fields.md](calcuated_fields.md)

### Troubleshooting
- **Configuration Loading Issues**: [FIELD_ID_TECHNICAL_NAME_MISMATCH_FIX.md](FIELD_ID_TECHNICAL_NAME_MISMATCH_FIX.md) - **LATEST FIX**
- **Performance Issues**: [PERFORMANCE_TESTING_GUIDE.md](PERFORMANCE_TESTING_GUIDE.md)
- **Operation Cost Issues**: [OPERATION_COST_TROUBLESHOOTING.md](OPERATION_COST_TROUBLESHOOTING.md)
- **Development Issues**: [06_DEVELOPER_GUIDE.md](06_DEVELOPER_GUIDE.md) → Troubleshooting
- **User Issues**: [07_USER_GUIDE.md](07_USER_GUIDE.md) → Troubleshooting
- **Odoo 18 Issues**: [ODOO_18_GUIDELINES.md](ODOO_18_GUIDELINES.md)

## 📝 Documentation Standards

### File Naming Convention
- **Core Documentation**: `XX_DESCRIPTION.md` (e.g., `00_OVERVIEW.md`)
- **Feature Documentation**: `DESCRIPTION.md` (e.g., `BOM_GENERATION.md`)
- **Technical Documentation**: `description.md` (e.g., `python_dictionary.md`)
- **Project Documentation**: `date_description.md` (e.g., `22june_tasks.md`)

### Content Structure
- **Overview**: High-level description and purpose
- **Features**: Detailed feature descriptions
- **Implementation**: Technical implementation details
- **Examples**: Code examples and use cases
- **Troubleshooting**: Common issues and solutions
- **References**: Related documentation and resources

### Maintenance
- **Regular Updates**: Documentation updated with each release
- **Version Control**: All documentation tracked in version control
- **Review Process**: Documentation reviewed for accuracy and completeness
- **User Feedback**: Documentation improved based on user feedback

## 🔗 External Resources

### Odoo Documentation
- [Odoo 18 Documentation](https://www.odoo.com/documentation/18.0/)
- [Odoo Development Guidelines](https://www.odoo.com/documentation/18.0/developer/)
- [Odoo API Reference](https://www.odoo.com/documentation/18.0/developer/reference/)

### Community Resources
- [Odoo Community Forum](https://www.odoo.com/forum/help-1)
- [Odoo GitHub](https://github.com/odoo/odoo)
- [Odoo Apps Store](https://apps.odoo.com/)

### Support & Contact
- **ITMS Group**: [Website](https://www.itmsgroup.com.au)
- **Email**: <EMAIL>
- **Documentation Issues**: Report via GitHub issues
- **Feature Requests**: Submit via GitHub issues

---

**Last Updated**: December 2024  
**Version**: 18.0.1.0.5  
**Maintained by**: ITMS Group 