# JavaScript Function Formula Fix - Implementation Plan

## Document Information
- **Created**: September 11, 2025
- **Module**: canbrax_configmatrix
- **Priority**: Critical
- **Target File**: `static/src/js/visibility_conditions.js`
- **Issue**: calculateDynamicFields method returns NaN for complex JavaScript function formulas

## Problem Overview

The `calculateDynamicFields` method in `visibility_conditions.js:376-526` cannot properly handle complex JavaScript function formulas, particularly IIFE (Immediately Invoked Function Expression) patterns used in calculated field definitions like those in `data/sws_window_calculated_fields.xml`.

### Affected Calculations
- SWS window corner angle calculations (`_CALCULATED_sws_corner_angle_a`, `_CALCULATED_sws_corner_angle_b`, etc.)
- Tolerance checking formulas (`_CALCULATED_sws_within_tolerance`)
- Dimensional calculations (`_CALCULATED_sws_overall_height`, `_CALCULATED_sws_overall_width`)

## Root Cause Analysis

### 1. IIFE Pattern Mishandling
**Problem**: Formulas use `(() => { ... })()` pattern, but current implementation wraps them incorrectly.

**Example Formula**:
```javascript
(() => {
    const SQRH = sws_wscrn_square_make_height_mm || 0;
    const SQRW = sws_wscrn_square_make_width_mm || 0;
    // Complex trigonometric calculations
    return Math.acos(Math.max(-1, Math.min(1, cosA))) * (180 / Math.PI);
})()
```

**Current Issue**: Line 487 tries to evaluate `const result = (${calcField.formula});` which creates invalid JavaScript.

### 2. XML Entity Encoding Issues
**Problem**: XML contains encoded entities (`&gt;`, `&lt;`, `&amp;`) that break JavaScript execution.

**Example**: `angle &gt;= 88 &amp;&amp; angle &lt;= 92` needs to become `angle >= 88 && angle <= 92`

### 3. Variable Scope Problems
**Problem**: IIFE creates its own closure, but field variables are passed as function parameters in wrong scope.

**Issue**: Field variables like `sws_wscrn_square_make_height_mm` are not accessible within IIFE closure.

### 4. Function Construction Errors
**Problem**: `new Function(...argNames, safeFormula)` creates incorrect function signatures for IIFE patterns.

## Simplified Implementation Plan

### Overview
This plan focuses on fixing the core IIFE pattern handling issue with minimal complexity. The backend will have a manual `formula_type` field to distinguish between JavaScript functions and conditional expressions, eliminating the need for auto-detection.

### Supported Formula Types
1. **`javascript_function`**: For IIFE patterns and JavaScript functions requiring special evaluation
2. **`conditional`**: For existing conditional logic (default behavior)

### Phase 1: Backend Formula Type Field
**Priority**: Critical
**Location**: Add to `config_matrix_calculated_field.py` around line 47

```python
# Add after line 47 (after data_type field)
formula_type = fields.Selection([
    ('conditional', 'Conditional Expression (Default)'),
    ('javascript_function', 'JavaScript Function/IIFE')
], string="Formula Type", default='conditional', 
   help="Specify how this formula should be evaluated in the frontend")
```

### Phase 2: Update Backend API
**Priority**: Critical  
**Location**: Modify `get_calculated_fields_for_template` method around line 151

```python
# Update return statement to include formula_type
return [{
    'name': field.name,
    'formula': field.formula,
    'formula_type': field.formula_type,  # ADD THIS LINE
    'description': field.description,
    'sequence': field.sequence,
    'category': field.category,
    'data_type': field.data_type,
    'is_pricing_matrix': field.id in height_field_ids or field.id in width_field_ids
} for field in fields]
```

### Phase 3: XML Entity Decoder
**Priority**: Critical
**Location**: Add helper function before calculateDynamicFields in `visibility_conditions.js`

```javascript
/**
 * Decode XML entities in formula strings
 * @param {string} formula - Formula with XML entities
 * @returns {string} - Formula with decoded entities
 */
function decodeXmlEntities(formula) {
    if (!formula || typeof formula !== 'string') {
        return formula;
    }
    
    return formula
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&apos;/g, "'");
}
```

### Phase 4: Enhanced Context Builder
**Priority**: High
**Location**: Replace lines 401-422 in calculateDynamicFields

```javascript
/**
 * Build evaluation context with mathematical functions
 * @param {Object} fieldValues - Current field values
 * @param {Object} results - Current calculation results
 * @returns {Object} - Complete evaluation context
 */
function buildEvaluationContext(fieldValues, results) {
    const context = { ...fieldValues, ...results };
    
    // Add Math object and mathematical functions for IIFE support
    context.Math = Math;
    Object.assign(context, {
        // Basic math functions
        min: Math.min,
        max: Math.max,
        abs: Math.abs,
        round: Math.round,
        ceil: Math.ceil,
        floor: Math.floor,
        sqrt: Math.sqrt,
        pow: Math.pow,
        
        // Trigonometric functions (needed for SWS calculations)
        acos: Math.acos,
        asin: Math.asin,
        atan: Math.atan,
        atan2: Math.atan2,
        cos: Math.cos,
        sin: Math.sin,
        tan: Math.tan,
        
        // Constants
        PI: Math.PI,
        E: Math.E
    });
    
    // Ensure numeric fields are properly converted
    Object.keys(context).forEach(key => {
        const value = context[key];
        if (typeof value === 'string' && !isNaN(value) && value.trim() !== '') {
            context[key] = parseFloat(value);
        } else if (typeof value === 'string' && value.trim() === '') {
            context[key] = 0;
        }
    });
    
    return context;
}
```

### Phase 5: Formula Evaluation Logic
**Priority**: Critical
**Location**: Replace lines 485-500 in calculateDynamicFields

```javascript
/**
 * Evaluate formula based on backend-specified type
 * @param {Object} calcField - Calculated field definition with formula_type
 * @param {Object} context - Evaluation context with field values
 * @returns {*} - Calculated result
 */
function evaluateFormulaByType(calcField, context) {
    const decodedFormula = decodeXmlEntities(calcField.formula);
    const formulaType = calcField.formula_type || 'conditional';
    
    if (window.debugConditions) {
        console.log(`[CALC-DEBUG] Evaluating ${calcField.name} as ${formulaType}`);
    }
    
    let result;
    
    try {
        if (formulaType === 'javascript_function') {
            // Handle JavaScript functions and IIFE patterns
            result = evaluateJavaScriptFunction(decodedFormula, context);
        } else {
            // Handle conditional expressions (default)
            result = evaluateConditionalExpression(decodedFormula, context);
        }
        
        // Validate result
        if (result === undefined || (typeof result === 'number' && isNaN(result))) {
            if (window.debugConditions) {
                console.warn(`[CALC-DEBUG] ${calcField.name} returned invalid result:`, result);
            }
            result = null;
        }
        
    } catch (error) {
        if (error instanceof ReferenceError) {
            if (window.debugConditions) {
                console.warn(`[CALC-DEBUG] Missing dependency in ${calcField.name}:`, error.message);
            }
            result = null;
        } else {
            console.error(`[CALC-ERROR] Error calculating ${calcField.name}:`, error);
            result = null;
        }
    }
    
    return result;
}

/**
 * Evaluate JavaScript functions and IIFE patterns
 */
function evaluateJavaScriptFunction(formula, context) {
    // Check if it's an IIFE pattern: (() => { ... })()
    const iifeMatch = formula.match(/^\s*\(\s*\(\s*\)\s*=>\s*\{([\s\S]*)\}\s*\)\s*\(\s*\)\s*$/);
    
    if (iifeMatch) {
        // Handle IIFE: extract inner content and execute with context
        const innerContent = iifeMatch[1];
        
        // Create context variables as const declarations
        const contextDeclarations = Object.keys(context)
            .map(key => {
                const value = context[key];
                if (typeof value === 'string') {
                    return `const ${key} = ${JSON.stringify(value)};`;
                } else {
                    return `const ${key} = ${value};`;
                }
            })
            .join('\n');
        
        // Execute with context
        const fullCode = `${contextDeclarations}\n${innerContent}`;
        const func = new Function(fullCode);
        return func();
    } else {
        // Handle other JavaScript function patterns
        const contextDeclarations = Object.keys(context)
            .map(key => {
                const value = context[key];
                if (typeof value === 'string') {
                    return `const ${key} = ${JSON.stringify(value)};`;
                } else {
                    return `const ${key} = ${value};`;
                }
            })
            .join('\n');
        
        const fullCode = `${contextDeclarations}\nreturn (${formula});`;
        const func = new Function(fullCode);
        return func();
    }
}

/**
 * Evaluate conditional expressions (existing logic)
 */
function evaluateConditionalExpression(formula, context) {
    const argNames = Object.keys(context);
    const argValues = argNames.map(name => context[name]);
    const func = new Function(...argNames, `return ${formula};`);
    return func(...argValues);
}
```

### Phase 6: Update Frontend Loading
**Priority**: Medium
**Location**: Modify lines 310-318 in loadCalculatedFields function

```javascript
// Update field mapping to include formula_type
calculatedFieldsDefinitions = result.result
    .map(field => ({
        name: field.name,
        formula: field.formula,
        formula_type: field.formula_type || 'conditional',  // ADD THIS LINE
        description: field.description || '',
        sequence: field.sequence || 10,
        category: field.category || 'basic',
        is_pricing_matrix: field.is_pricing_matrix || false,
    }))
    .sort((a, b) => a.sequence - b.sequence);
```

### Phase 7: Integrate New Evaluation Logic
**Priority**: Critical
**Location**: Replace formula evaluation in calculateDynamicFields (around line 487)

```javascript
// Replace the existing formula evaluation logic with:
const result = evaluateFormulaByType(calcField, safeContext);
```

## Testing Strategy

### Unit Tests
1. **XML Entity Decoding**
   - Test all common XML entities (`&gt;`, `&lt;`, `&amp;`)
   - Verify decoded formulas execute correctly

2. **IIFE Pattern Handling**
   - Test IIFE pattern recognition and execution
   - Verify context variable injection works correctly

3. **Context Building**
   - Test mathematical function availability
   - Test numeric field conversion

### Integration Tests
1. **SWS Window Calculations**
   - Test corner angle calculations with real data
   - Verify tolerance checking formulas work correctly
   - Test dimensional calculations

2. **Formula Type Configuration**
   - Test manual formula_type selection in backend
   - Verify frontend respects backend formula_type

### Performance Tests
1. **Calculation Speed**
   - Measure performance impact of new evaluation logic
   - Compare IIFE vs conditional execution times

## Implementation Steps

### Step 1: Backend Changes
1. Add `formula_type` field to `ConfigMatrixCalculatedField` model
2. Update `get_calculated_fields_for_template` to return formula_type
3. Set formula_type for existing SWS calculated fields to `javascript_function`

### Step 2: Frontend Changes
1. Add XML entity decoder helper function
2. Add enhanced context builder with mathematical functions
3. Add IIFE-aware evaluation functions
4. Update frontend loading to use formula_type
5. Integrate new evaluation logic into calculateDynamicFields

### Step 3: Configuration
1. Manually set formula_type for SWS angle calculation fields to `javascript_function`
2. Leave other fields as `conditional` (default)

### Step 4: Testing
1. Test with SWS window calculated fields
2. Verify existing conditional logic still works
3. Test edge cases and error conditions

## Expected Outcomes

After implementation:

✅ **IIFE formulas will execute correctly**
- SWS corner angle calculations will return proper degree values instead of NaN
- Complex mathematical operations will work as expected

✅ **XML entity handling will be robust**
- Comparison operators (`>=`, `<=`, `&&`) will work in formulas
- Boolean expressions will evaluate correctly

✅ **Manual configuration control**
- Administrators can specify formula_type per calculated field
- No automatic detection complexity

✅ **Backward compatibility maintained**
- Existing conditional logic continues to work unchanged

## Risk Assessment

### Low Risk
- Adding optional backend field with default value
- XML entity decoding helper function
- Enhanced context building

### Medium Risk
- New IIFE evaluation logic
- Integration with existing calculateDynamicFields method

### High Risk
- None - simplified approach minimizes risk

## Success Metrics

- [ ] All SWS corner angle calculations return valid numeric values (not NaN)
- [ ] Tolerance checking formulas evaluate correctly
- [ ] No regression in existing calculated field functionality
- [ ] Manual formula_type configuration works correctly
- [ ] Integration tests with real configuration data pass

## Notes

- This simplified approach focuses only on fixing the IIFE pattern issue
- Manual formula_type configuration eliminates auto-detection complexity
- The implementation maintains backward compatibility
- Only 2 formula types needed: `javascript_function` and `conditional`

---

## Cross-File Dependency Impact Assessment

### **Compatibility Analysis Complete** ✅
Based on comprehensive analysis in `CALCULATED_FIELDS_DEPENDENCY_ANALYSIS.md`:

- **5 JavaScript files analyzed** - Only 1 requires changes
- **12 Python files analyzed** - Compatible with new field  
- **3 data files analyzed** - Require formula_type migration
- **Risk Level**: 🟢 **LOW RISK** - No breaking changes identified

### **Required Dependency Updates**

#### **Frontend Updates (1 file)**
**File**: `static/src/js/visibility_conditions.js`  
**Lines**: 310-318 (loadCalculatedFields function)  
**Change**: Add formula_type to field mapping  
**Impact**: Enables proper formula evaluation

#### **Data Migration Updates (2 files)**  
**Files**: `data/sws_window_calculated_fields.xml`, `data/mesh_calculated_fields.xml`  
**Change**: Add formula_type field to all calculated field records  
**Impact**: Ensures proper formula evaluation for existing fields

**SWS Window Fields Requiring `javascript_function`:**
- `_CALCULATED_sws_corner_angle_a` (IIFE formula)
- `_CALCULATED_sws_corner_angle_b` (IIFE formula) 
- `_CALCULATED_sws_corner_angle_c` (IIFE formula)
- `_CALCULATED_sws_corner_angle_d` (IIFE formula)
- `_CALCULATED_sws_within_tolerance` (IIFE formula)
- `_CALCULATED_sws_overall_height` (IIFE formula)
- `_CALCULATED_sws_overall_width` (IIFE formula)

**All Other Fields Default to:** `conditional`

#### **Import Wizard Compatibility (1 file)**
**File**: `wizards/config_matrix_calculated_field_import_wizard.py`  
**Change**: Handle formula_type in JSON imports with fallback to 'conditional'  
**Impact**: Maintains import functionality for existing and new data

### **Updated Implementation Steps**

#### **Step 1: Backend Model Changes** (0.5 day)
1. Add `formula_type` field to ConfigMatrixCalculatedField model
2. Update `get_calculated_fields_for_template` to return formula_type
3. Update import wizard to handle formula_type field

#### **Step 2: Data Migration** (0.5 day)  
1. Update SWS window calculated fields with correct formula_type values
2. Update mesh calculated fields with default 'conditional' type
3. Test data migration on development environment

#### **Step 3: Frontend Integration** (1 day)
1. Update visibility_conditions.js field loading 
2. Add XML entity decoder helper function
3. Add enhanced context builder with Math functions
4. Add IIFE-aware evaluation functions
5. Integrate new evaluation logic into calculateDynamicFields

#### **Step 4: Testing & Validation** (0.5 day)
1. Test SWS angle calculations return proper numeric values
2. Verify mesh calculations continue working  
3. Test import wizard with new formula_type field
4. Validate no regression in existing functionality

### **Migration Safety Measures**

#### **Deployment Strategy**
1. **Phase 1**: Deploy backend changes (API backward compatible)
2. **Phase 2**: Run data migration during maintenance window
3. **Phase 3**: Deploy frontend changes after backend is stable
4. **Phase 4**: Monitor for any issues and rollback if needed

#### **Rollback Plan**
1. Restore calculated field data from backup
2. Revert frontend changes to previous version
3. Revert backend model changes if necessary
4. Validate system returns to working state

### **Success Validation Checklist**

- [ ] All SWS corner angle calculations return valid numeric values (not NaN)
- [ ] Tolerance checking formulas evaluate correctly (boolean values)
- [ ] Mesh calculations continue to work without regression
- [ ] Import wizard handles both old and new formula format  
- [ ] No JavaScript console errors in configurator
- [ ] API endpoints return expected formula_type field
- [ ] Data migration completes without errors
- [ ] Rollback procedure tested and confirmed working

---

**Document Status**: ✅ **Ready for Implementation** (Dependency Analysis Complete)  
**Risk Assessment**: 🟢 **LOW RISK** (No breaking changes identified)  
**Next Review**: After data migration completion  
**Estimated Effort**: 
- **Backend Model Changes**: 0.5 day
- **Data Migration**: 0.5 day  
- **Frontend Implementation**: 1 day  
- **Testing & Validation**: 0.5 day
- **Total**: 2.5 days (includes dependency compatibility work)