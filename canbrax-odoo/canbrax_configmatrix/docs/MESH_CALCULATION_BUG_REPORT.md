# Mesh Calculation Bug Report

## Problem Summary
The `_CALCULATED_mesh_series` field is not being calculated and saved properly, causing mesh panels to not be added to the BOM during configuration confirmation.

## Expected Behavior
- `_CALCULATED_mesh_series` should calculate to `'basix'` (from template)
- Mesh panels should be added to BOM when configuration is confirmed
- This was working perfectly on commit `92d3819`

## Current Behavior
- `_CALCULATED_mesh_series` shows as `null` in config data
- No mesh panels are added to BOM
- Backend fallback works: `Final mesh_series used: basix`
- But the calculated field itself is not being saved

## Evidence from Logs

### Template Configuration ✅
```
[MESH_DEBUG] Template loaded: Subcategory - DBL Hinged BX (IMPORT), mesh_series: basix
[MESH_DEBUG] Added template_mesh_series to context: basix for template Subcategory - DBL Hinged BX (IMPORT)
```

### Other Mesh Calculations Work ✅
```
[MESH_DEBUG] Pass 1: Calculated _CALCULATED_mesh_required = True
[MESH_DEBUG] Pass 1: Calculated _CALCULATED_mesh_width = 700
[MESH_DEBUG] Pass 1: Calculated _CALCULATED_mesh_height = 1900.0
[MESH_DEBUG] Pass 1: Calculated _CALCULATED_mesh_area = 1330000.0
[MESH_DEBUG] Pass 1: Calculated _CALCULATED_mesh_operation_required = True
```

### Formula Conversion Happens ✅
```
[MESH_DEBUG] Original formula: _CALCULATED_mesh_required ? (mesh_series || template_mesh_series || 'saltwaterseries') : ''
[MESH_DEBUG] Converted formula: (mesh_series  or  template_mesh_series  or  'saltwaterseries' if (_CALCULATED_mesh_required) else '')
```

### But Result Never Logged ❌
**Missing**: `[MESH_DEBUG] Pass 1: Calculated _CALCULATED_mesh_series = basix`

### Config Data Shows Null ❌
```
[MESH_DEBUG] _CALCULATED_mesh_series from config: None
```

### Backend Fallback Works ✅
```
[MESH_DEBUG] template_id.mesh_series fallback: basix
[MESH_DEBUG] Final mesh_series used: basix
```

### But No Mesh Added to BOM ❌
```
[MESH-DEBUG] Dynamic mesh requirement is False - no panels needed
```

## Technical Analysis

### The Issue
1. **Formula conversion works** - JavaScript to Python conversion is correct
2. **Context has required data** - `template_mesh_series = 'basix'`
3. **Other calculations work** - All other mesh fields calculate properly
4. **But `_CALCULATED_mesh_series` fails silently** - No result logged, no error logged

### The Formula
- **JavaScript**: `_CALCULATED_mesh_required ? (mesh_series || template_mesh_series || 'saltwaterseries') : ''`
- **Python**: `(mesh_series  or  template_mesh_series  or  'saltwaterseries' if (_CALCULATED_mesh_required) else '')`

### Expected Evaluation
With context:
- `_CALCULATED_mesh_required = True`
- `mesh_series = undefined` (should cause NameError)
- `template_mesh_series = 'basix'`

Should evaluate to: `'basix'`

### Suspected Root Cause
The `mesh_series` variable is undefined in the Python context, causing a `NameError` that's being caught by the exception handler, but the multi-pass system isn't resolving it properly.

## What Changed
- This was working on commit `92d3819` (Aug 28, 2025)
- Recent changes focused on SVG integration (commit `7df8c44`)
- No direct changes to mesh calculation logic visible

## Files Involved
- `canbrax_configmatrix/models/config_matrix_calculated_field.py` - Calculation logic
- `canbrax_configmatrix/models/config_matrix_configuration.py` - BOM creation logic

## Debugging Steps Taken
1. Added extensive logging to track formula conversion
2. Added error logging for `_CALCULATED_mesh_series` specifically
3. Removed `mesh_series = None` context addition (suspected cause)
4. Confirmed template data is correct
5. Confirmed other mesh calculations work

## Next Steps Needed
1. Identify why `_CALCULATED_mesh_series` calculation fails silently
2. Check if exception handling is swallowing the error
3. Compare with working commit `92d3819`
4. Fix the calculation so mesh panels are added to BOM again
