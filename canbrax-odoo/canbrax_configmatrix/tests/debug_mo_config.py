#!/usr/bin/env python3
"""
Debug script to check MO and BOM config relationships
Run this in Odoo shell to debug the specific MO
"""

def debug_mo_config():
    """Debug MO config relationship"""
    print("=== MO CONFIG DEBUG ===")
    print()
    
    # Find the latest MO
    mo = env['mrp.production'].search([], order='id desc', limit=1)
    if not mo:
        print("❌ No Manufacturing Orders found")
        return
    
    print(f"🏭 Manufacturing Order: {mo.name}")
    print(f"   Product: {mo.product_id.name}")
    print(f"   State: {mo.state}")
    print()
    
    print("📋 CONFIG RELATIONSHIPS:")
    print(f"   MO config_id: {mo.config_id.id if mo.config_id else 'None'}")
    print(f"   MO is_configured: {mo.is_configured}")
    print()
    
    if mo.bom_id:
        bom = mo.bom_id
        print(f"   BOM: {bom.code}")
        print(f"   BOM config_id: {bom.config_id.id if bom.config_id else 'None'}")
        print(f"   BOM is_configured: {bom.is_configured}")
        print(f"   BOM byproducts: {len(bom.byproduct_ids)}")
        
        for bp in bom.byproduct_ids:
            print(f"     - {bp.product_id.name} (qty: {bp.product_qty})")
    else:
        print("   No BOM found")
    
    print()
    print("🔗 MESH OPERATIONS:")
    mesh_ops = env['mesh.cut.operation'].search([
        '|',
        ('production_id', '=', mo.id),
        ('config_id', '=', mo.config_id.id if mo.config_id else False)
    ])
    
    print(f"   Found {len(mesh_ops)} mesh operations")
    for op in mesh_ops:
        print(f"     Op {op.id}: {op.name}")
        print(f"       Config: {op.config_id.id if op.config_id else 'None'}")
        print(f"       Production: {op.production_id.name if op.production_id else 'None'}")
        print(f"       Cut Plan: {op.cut_plan_id.name if op.cut_plan_id else 'None'}")
        print(f"       State: {op.state}")
        
        if op.cut_plan_id:
            print(f"       Cut Plan Byproducts: {len(op.cut_plan_id.byproduct_ids)}")
            for bp in op.cut_plan_id.byproduct_ids:
                print(f"         - {bp.product_id.name if bp.product_id else 'No Product'} (qty: {bp.quantity})")
    
    print()
    print("🎯 INTEGRATION TEST:")
    
    # Test the config fallback logic
    config_id = mo.config_id or (mo.bom_id.config_id if mo.bom_id else None)
    if config_id:
        print(f"   ✅ Config found: {config_id.id}")
        
        # Find mesh operations for this config
        config_mesh_ops = env['mesh.cut.operation'].search([
            ('config_id', '=', config_id.id),
            ('cut_plan_id', '!=', False)
        ])
        print(f"   ✅ Mesh operations with cut plans: {len(config_mesh_ops)}")
        
        total_byproducts = 0
        for op in config_mesh_ops:
            if op.cut_plan_id:
                total_byproducts += len(op.cut_plan_id.byproduct_ids)
        
        print(f"   ✅ Total byproducts available: {total_byproducts}")
        
        if total_byproducts > 0:
            print("   🚀 Integration should work!")
        else:
            print("   ⚠️  No byproducts to integrate")
    else:
        print("   ❌ No config found - integration will fail")

if __name__ == "__main__":
    print("Run this in Odoo shell:")
    print("exec(open('canbrax_configmatrix/debug_mo_config.py').read())")
    print("debug_mo_config()")
